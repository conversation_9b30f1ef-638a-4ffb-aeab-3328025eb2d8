// lib/screens/todays_shots_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:gal/gal.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:math';

import '../utils/image_cache_manager.dart';
import '../utils/background_manager.dart';
import '../utils/storage_permission_manager.dart';
import '../widgets/app_drawer.dart';
import '../widgets/custom_app_bar.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';
import '../data/pickup_lines_data.dart';

class TodaysShotsScreen extends StatefulWidget {
  const TodaysShotsScreen({super.key});

  @override
  State<TodaysShotsScreen> createState() => _TodaysShotsScreenState();
}

class _TodaysShotsScreenState extends State<TodaysShotsScreen> {
  final Random _random = Random();

  // Screenshot controller for saving images
  final ScreenshotController _screenshotController = ScreenshotController();

  // Random pickup lines collection from all categories
  List<String> _pickupLines = [];

  // Background images managed by BackgroundManager (supports future expansion to 20+)
  final List<String> backgroundImages = BackgroundManager.getAllBackgrounds();

  String _currentLine = "";
  String _currentBackground = "";

  @override
  void initState() {
    super.initState();
    // Load pickup lines from all categories
    _loadAllPickupLines();
    _generateRandomShot();
  }

  void _loadAllPickupLines() {
    // Load all pickup lines from both languages
    _pickupLines = PickupLinesData.getAllLines();
  }

  void _generateRandomShot() {
    setState(() {
      _currentLine = _pickupLines[_random.nextInt(_pickupLines.length)];
      // Use BackgroundManager for better random background selection
      _currentBackground = BackgroundManager.getRandomBackgroundExcluding(
        _currentBackground,
      );
    });
  }

  // Save image to gallery with persistent permission handling
  Future<void> _saveToGallery() async {
    try {
      final permissionManager = StoragePermissionManager.instance;

      // Check if we already have permission
      bool hasPermission = await permissionManager.hasStoragePermission();

      // Request permission if we don't have it
      if (!hasPermission) {
        hasPermission = await permissionManager.requestStoragePermission();
      }

      if (hasPermission) {
        // Capture screenshot
        final Uint8List? imageBytes = await _screenshotController.capture();

        if (imageBytes != null) {
          // Save to gallery using Gal
          try {
            await Gal.putImageBytes(
              imageBytes,
              name: "charm_shot_${DateTime.now().millisecondsSinceEpoch}",
            );

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Image saved to gallery!'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to save image: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Storage permission required to save images'),
              backgroundColor: Colors.orange,
              action: SnackBarAction(
                label: 'Settings',
                onPressed: () => permissionManager.openAppSettings(),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, FavoritesProvider>(
      builder: (context, themeProvider, favoritesProvider, child) {
        return Scaffold(
          backgroundColor: themeProvider.isDarkMode
              ? Colors.grey.shade900
              : Colors.white,
          appBar: Custom3DAppBar(
            title: "Today's Shots",
            actions: [
              IconButton(
                icon: Icon(Icons.refresh),
                onPressed: _generateRandomShot,
                tooltip: 'Get New Shot',
              ),
            ],
          ),
          drawer: AppDrawer(),
          body: Container(
            color: Colors.white,
            child: Center(
              child: Container(
                margin: EdgeInsets.all(12), // Reduced margin for consistency
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    // Optimized 3D shadow effect
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.15),
                      spreadRadius: 0,
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.08),
                      spreadRadius: 0,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Screenshot area - only quote and background
                    GestureDetector(
                      onTap: () {
                        // Make post reactive - change background and play sound
                        _generateRandomShot();
                        if (favoritesProvider.tapSoundEnabled) {
                          SystemSound.play(SystemSoundType.click);
                        }
                      },
                      child: Screenshot(
                        controller: _screenshotController,
                        child: Container(
                          height: 420, // Only the quote and background area
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(24),
                              topRight: Radius.circular(24),
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(24),
                              topRight: Radius.circular(24),
                            ),
                            child: Stack(
                              children: [
                                // Background Image - Fast loading without fade
                                Positioned.fill(
                                  child: OptimizedAssetImage(
                                    imagePath: _currentBackground,
                                    fit: BoxFit.cover,
                                    filterQuality: FilterQuality.low,
                                    enableFadeIn:
                                        false, // Disable fade for faster loading
                                    errorWidget: Container(
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            Colors.deepPurple.shade400,
                                            Colors.purple.shade600,
                                            Colors.pink.shade500,
                                          ],
                                        ),
                                      ),
                                      child: Center(
                                        child: Icon(
                                          Icons.favorite,
                                          color: Colors.white54,
                                          size: 48,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                // No overlay - clean background image only
                                // Content - only quote content for screenshot
                                Positioned.fill(
                                  child: Container(
                                    padding: EdgeInsets.all(24),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        // Quote content
                                        Expanded(
                                          child: Container(
                                            padding: EdgeInsets.symmetric(
                                              vertical: 20,
                                              horizontal: 16,
                                            ),
                                            child: Center(
                                              child: RichText(
                                                textAlign: TextAlign.center,
                                                text: TextSpan(
                                                  style: TextStyle(
                                                    fontSize: 20,
                                                    color: Colors.white,
                                                    fontWeight: FontWeight.bold,
                                                    height: 1.4,
                                                    shadows: [
                                                      Shadow(
                                                        color: Colors.black
                                                            .withValues(
                                                              alpha: 0.5,
                                                            ),
                                                        offset: Offset(1, 1),
                                                        blurRadius: 2,
                                                      ),
                                                    ],
                                                  ),
                                                  children: [
                                                    TextSpan(
                                                      text: '"',
                                                      style: TextStyle(
                                                        fontSize: 28,
                                                        fontWeight:
                                                            FontWeight.w900,
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text: _currentLine,
                                                    ),
                                                    TextSpan(
                                                      text: '"',
                                                      style: TextStyle(
                                                        fontSize: 28,
                                                        fontWeight:
                                                            FontWeight.w900,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Action buttons - integrated at bottom of post (outside screenshot area)
                    Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 16,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(24),
                          bottomRight: Radius.circular(24),
                        ),
                        // Inner highlight for 3D effect
                        boxShadow: [
                          BoxShadow(
                            color: Colors.white.withValues(alpha: 0.8),
                            spreadRadius: 0,
                            blurRadius: 1,
                            offset: const Offset(0, -1),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildActionButton(
                            icon: Icons.refresh,
                            label: 'New',
                            onTap: _generateRandomShot,
                          ),
                          Builder(
                            builder: (context) {
                              final postId = 'today_${_currentLine.hashCode}';
                              final isFavorite = favoritesProvider.isFavorite(
                                postId,
                              );
                              return _buildActionButton(
                                icon: isFavorite
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                label: isFavorite ? 'Liked' : 'Like',
                                onTap: () {
                                  favoritesProvider.toggleFavorite(
                                    postId,
                                    _currentLine,
                                    _currentBackground,
                                  );
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        isFavorite
                                            ? 'Removed from favorites!'
                                            : 'Added to favorites!',
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                          _buildActionButton(
                            icon: Icons.copy,
                            label: 'Copy',
                            onTap: () {
                              Clipboard.setData(
                                ClipboardData(text: _currentLine),
                              );
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('Copied to clipboard!')),
                              );
                            },
                          ),
                          _buildActionButton(
                            icon: Icons.download,
                            label: 'Save',
                            onTap: _saveToGallery,
                          ),
                          _buildActionButton(
                            icon: Icons.share,
                            label: 'Share',
                            onTap: () {
                              // Share the pickup line with app branding
                              SharePlus.instance.share(
                                ShareParams(
                                  text:
                                      '$_currentLine\n\n💕 Shared from Charm Shots - The ultimate pickup lines app!',
                                  subject: 'Check out this pickup line!',
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 6),
          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(16), // Rounded buttons
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                spreadRadius: 0,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: Colors.black87,
                size: 26,
              ), // Slightly larger icons
              const SizedBox(height: 6),
              Text(
                label,
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 13, // Slightly larger text
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
