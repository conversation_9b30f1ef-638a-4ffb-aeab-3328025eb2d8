// lib/main.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/splash_screen.dart';
import 'screens/language_selection_screen.dart';
import 'screens/category_screen.dart';
import 'screens/lines_list_screen.dart';
import 'screens/performance_debug_screen.dart';
import 'screens/favorites_screen.dart';
import 'utils/image_cache_manager.dart';
import 'utils/performance_utils.dart';
import 'utils/performance_optimizer.dart';
import 'utils/storage_permission_manager.dart';
import 'providers/theme_provider.dart';
import 'providers/favorites_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => FavoritesProvider()),
      ],
      child: CharmShotsApp(),
    ),
  );
}

class CharmShotsApp extends StatefulWidget {
  const CharmShotsApp({super.key});

  @override
  State<CharmShotsApp> createState() => _CharmShotsAppState();
}

class _CharmShotsAppState extends State<CharmShotsApp> {
  @override
  void initState() {
    super.initState();
    // Initialize performance monitoring and image preloading after startup
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeAfterStartup();
    });
  }

  void _initializeAfterStartup() {
    // Initialize performance optimization
    PerformanceOptimizer.setOptimizationEnabled(true);

    // Initialize favorites provider
    context.read<FavoritesProvider>().initialize();

    // Initialize storage permission manager
    StoragePermissionManager.instance.initialize();

    // Start performance monitoring after app is stable
    Future.delayed(const Duration(seconds: 2), () {
      PerformanceUtils.startFrameMonitoring();
    });

    // Only preload images when user actually navigates to lines screen
    // This eliminates startup frame drops completely
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp(
          title: 'Charm Shots',
          theme: themeProvider.currentTheme,
          navigatorKey: NavigationService.navigatorKey,
          initialRoute: '/',
          // Add explicit text direction to fix Directionality issues
          builder: (context, child) {
            return Directionality(
              textDirection: TextDirection.ltr,
              child: child ?? Container(),
            );
          },
          // Enhanced page transitions for smoother animations
          onGenerateRoute: (settings) {
            return PageRouteBuilder(
              settings: settings,
              pageBuilder: (context, animation, secondaryAnimation) {
                Widget page;
                switch (settings.name) {
                  case '/':
                    page = SplashScreen();
                    break;
                  case '/language':
                    page = LanguageSelectScreen();
                    break;
                  case '/categories':
                    final args = settings.arguments as String? ?? 'English';
                    page = CategoryScreen(language: args);
                    break;
                  case '/lines':
                    final args = settings.arguments;
                    if (args is Map<String, String>) {
                      page = LinesListScreen(
                        category: args['category'] ?? 'Bold',
                        language: args['language'] ?? 'English',
                      );
                    } else {
                      page = LinesListScreen(
                        category: args as String? ?? 'Bold',
                        language: 'English',
                      );
                    }
                    break;
                  case '/performance-debug':
                    page = PerformanceDebugScreen();
                    break;
                  case '/favorites':
                    page = FavoritesScreen();
                    break;
                  default:
                    page = SplashScreen();
                }
                return page;
              },
              transitionDuration: const Duration(
                milliseconds: 100,
              ), // Fast transitions without slide
              reverseTransitionDuration: const Duration(milliseconds: 100),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                    // Simple fade transition only - no slide animation
                    return FadeTransition(opacity: animation, child: child);
                  },
            );
          },
        );
      },
    );
  }
}
