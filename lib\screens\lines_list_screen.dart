// lib/screens/lines_list_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:gal/gal.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';

import '../utils/storage_permission_manager.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';
import '../data/pickup_lines_data.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/enhanced_edit_dialog.dart';
import '../models/styled_text.dart';

class LinesListScreen extends StatefulWidget {
  final String category;
  final String language;
  const LinesListScreen({
    super.key,
    required this.category,
    required this.language,
  });

  @override
  State<LinesListScreen> createState() => _LinesListScreenState();
}

class _LinesListScreenState extends State<LinesListScreen>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  final List<String> backgroundImages = [
    'assets/backgrounds/bg1.jpg',
    'assets/backgrounds/bg2.jpg',
    'assets/backgrounds/bg3.jpg',
    'assets/backgrounds/bg4.jpg',
    'assets/backgrounds/bg5.jpg',
    'assets/backgrounds/bg6.jpg',
    'assets/backgrounds/bg7.jpg',
    'assets/backgrounds/bg8.jpg',
    'assets/backgrounds/bg9.jpg',
    'assets/backgrounds/bg10.jpg',
  ];

  final Map<int, ValueNotifier<int>> cardBackgroundNotifiers = {};
  final Map<int, ValueNotifier<bool>> cardLikedNotifiers = {};
  final Map<int, StyledText> styledTexts = {};
  DateTime? _lastTapTime;

  List<String> lines = [];
  final ScrollController _scrollController = ScrollController();

  // Helper method to remove quotes from text
  String _removeQuotes(String text) {
    return text
        .replaceAll(RegExp(r'^[""]'), '') // Remove opening quotes
        .replaceAll(RegExp(r'[""]$'), '') // Remove closing quotes
        .replaceAll('"', '') // Remove all double quotes
        .replaceAll("'", '') // Remove all single quotes
        .replaceAll('"', '') // Remove left double quote
        .replaceAll('"', '') // Remove right double quote
        .replaceAll(''', '') // Remove left single quote
        .replaceAll(''', '') // Remove right single quote
        .trim();
  }

  void _changeBackground(int index) {
    final now = DateTime.now();
    if (_lastTapTime != null &&
        now.difference(_lastTapTime!).inMilliseconds < 300) {
      return;
    }
    _lastTapTime = now;

    HapticFeedback.lightImpact();

    // Initialize notifier only when needed
    if (!cardBackgroundNotifiers.containsKey(index)) {
      cardBackgroundNotifiers[index] = ValueNotifier<int>(0);
    }

    final notifier = cardBackgroundNotifiers[index]!;
    final currentBg = notifier.value;
    final newBg = (currentBg + 1) % backgroundImages.length;
    notifier.value = newBg;
  }

  void _updateQuote(int index, String newQuote, [TextStyle? newStyle]) {
    if (mounted) {
      setState(() {
        lines[index] = _removeQuotes(newQuote); // Clean quotes when updating
        if (newStyle != null) {
          styledTexts[index] = StyledText.fromTextStyle(
            _removeQuotes(newQuote),
            newStyle,
          );
        }
      });
    }
  }

  // Initialize notifiers lazily only when needed
  void _initializeNotifier(int index) {
    if (!cardBackgroundNotifiers.containsKey(index)) {
      final randomBgIndex = (lines[index].hashCode % backgroundImages.length)
          .abs();
      cardBackgroundNotifiers[index] = ValueNotifier<int>(randomBgIndex);
    }

    if (!cardLikedNotifiers.containsKey(index)) {
      cardLikedNotifiers[index] = ValueNotifier<bool>(false);
    }
  }

  @override
  void initState() {
    super.initState();

    // Clean quotes from all lines at source
    final rawLines = PickupLinesData.getLinesForCategory(
      widget.category,
      widget.language,
    );
    lines = rawLines.map((line) => _removeQuotes(line)).toList();

    // Lazy image preloading after first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _preloadCriticalImages();
    });
  }

  // Optimized image preloading - only preload first few images
  void _preloadCriticalImages() {
    if (!mounted) return;

    Future.microtask(() {
      // Only preload first 3 images to avoid blocking UI
      final criticalImages = backgroundImages.take(3);
      for (String imagePath in criticalImages) {
        precacheImage(AssetImage(imagePath), context).catchError((_) {});
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    for (final notifier in cardBackgroundNotifiers.values) {
      notifier.dispose();
    }
    for (final notifier in cardLikedNotifiers.values) {
      notifier.dispose();
    }
    cardBackgroundNotifiers.clear();
    cardLikedNotifiers.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Consumer2<ThemeProvider, FavoritesProvider>(
      builder: (context, themeProvider, favoritesProvider, child) {
        final screenHeight = MediaQuery.of(context).size.height;
        final appBarHeight = kToolbarHeight;
        final statusBarHeight = MediaQuery.of(context).padding.top;
        final bottomPadding = MediaQuery.of(context).padding.bottom;
        final availableHeight =
            screenHeight - appBarHeight - statusBarHeight - bottomPadding - 40;
        final cardHeight = (availableHeight / 1.5) - 20;

        return Scaffold(
          backgroundColor: themeProvider.isDarkMode
              ? Colors.grey.shade900
              : Colors.grey.shade100,
          appBar: Custom3DAppBar(title: widget.category),
          body: Container(
            color: themeProvider.isDarkMode
                ? Colors.grey.shade900
                : Colors.grey.shade100,
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.fromLTRB(20, 30, 20, 20),
              itemCount: lines.length,
              // Performance optimizations
              cacheExtent: cardHeight * 2, // Cache 2 cards above/below
              addAutomaticKeepAlives: false, // Reduce memory usage
              addRepaintBoundaries: true, // Improve scroll performance
              addSemanticIndexes:
                  false, // Disable if not needed for accessibility
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 25),
                  child: SizedBox(
                    height: cardHeight,
                    child: _buildOptimizedQuoteCard(
                      index,
                      favoritesProvider,
                      cardHeight,
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildOptimizedQuoteCard(
    int index,
    FavoritesProvider favoritesProvider,
    double cardHeight,
  ) {
    // Initialize only when building this card
    _initializeNotifier(index);

    final postId = 'quote_${widget.category}_$index';
    cardLikedNotifiers[index]!.value = favoritesProvider.isFavorite(postId);

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return OptimizedMultiPostCard(
          key: ValueKey(
            'quote_card_$index',
          ), // Simplified key for better performance
          index: index,
          quote: lines[index], // Already cleaned in initState
          styledText: styledTexts[index],
          backgroundImages: backgroundImages,
          backgroundNotifier: cardBackgroundNotifiers[index]!,
          likedNotifier: cardLikedNotifiers[index]!,
          onBackgroundChange: () => _changeBackground(index),
          onQuoteUpdate: (newQuote, newStyle) =>
              _updateQuote(index, newQuote, newStyle),
          themeProvider: themeProvider,
          favoritesProvider: favoritesProvider,
          category: widget.category,
          cardHeight: cardHeight,
          language: widget.language,
        );
      },
    );
  }
}

class OptimizedMultiPostCard extends StatefulWidget {
  final int index;
  final String quote;
  final StyledText? styledText;
  final List<String> backgroundImages;
  final ValueNotifier<int> backgroundNotifier;
  final ValueNotifier<bool> likedNotifier;
  final VoidCallback onBackgroundChange;
  final Function(String, TextStyle?) onQuoteUpdate;
  final ThemeProvider themeProvider;
  final FavoritesProvider favoritesProvider;
  final String category;
  final double cardHeight;
  final String language;

  const OptimizedMultiPostCard({
    super.key,
    required this.index,
    required this.quote,
    this.styledText,
    required this.backgroundImages,
    required this.backgroundNotifier,
    required this.likedNotifier,
    required this.onBackgroundChange,
    required this.onQuoteUpdate,
    required this.themeProvider,
    required this.favoritesProvider,
    required this.category,
    required this.cardHeight,
    required this.language,
  });

  @override
  State<OptimizedMultiPostCard> createState() => _OptimizedMultiPostCardState();
}

class _OptimizedMultiPostCardState extends State<OptimizedMultiPostCard> {
  final ScreenshotController _screenshotController = ScreenshotController();
  bool _isSaving = false;

  // Enhanced quote cleaning method
  String _cleanQuoteText(String text) {
    return text
        .replaceAll(RegExp(r'^[""]'), '') // Remove opening quotes
        .replaceAll(RegExp(r'[""]$'), '') // Remove closing quotes
        .replaceAll('"', '') // Remove all double quotes
        .replaceAll("'", '') // Remove all single quotes
        .replaceAll('"', '') // Remove left double quote
        .replaceAll('"', '') // Remove right double quote
        .replaceAll(''', '') // Remove left single quote
        .replaceAll(''', '') // Remove right single quote
        .trim();
  }

  Future<void> _saveAsJPG() async {
    if (_isSaving) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final permissionManager = StoragePermissionManager.instance;
      bool hasPermission = await permissionManager.hasStoragePermission();
      if (!hasPermission) {
        hasPermission = await permissionManager.requestStoragePermission();
      }

      if (hasPermission) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 16),
                  Text('Saving image...'),
                ],
              ),
              backgroundColor: Colors.blue,
              duration: Duration(seconds: 2),
            ),
          );
        }

        final Uint8List? pngBytes = await _screenshotController.capture(
          delay: Duration(milliseconds: 100),
          pixelRatio: 3.0,
        );

        if (pngBytes != null) {
          final ui.Codec codec = await ui.instantiateImageCodec(pngBytes);
          final ui.FrameInfo frameInfo = await codec.getNextFrame();
          final ui.Image image = frameInfo.image;

          final ui.PictureRecorder recorder = ui.PictureRecorder();
          final Canvas canvas = Canvas(recorder);
          final Paint paint = Paint()..color = Colors.white;

          canvas.drawRect(
            Rect.fromLTWH(
              0,
              0,
              image.width.toDouble(),
              image.height.toDouble(),
            ),
            paint,
          );
          canvas.drawImage(image, Offset.zero, Paint());

          final ui.Picture picture = recorder.endRecording();
          final ui.Image finalImage = await picture.toImage(
            image.width,
            image.height,
          );

          final ByteData? jpgByteData = await finalImage.toByteData(
            format: ui.ImageByteFormat.png,
          );

          if (jpgByteData != null) {
            final Uint8List jpgBytes = jpgByteData.buffer.asUint8List();
            final String fileName =
                "charm_shot_${widget.category.toLowerCase()}_${DateTime.now().millisecondsSinceEpoch}";

            await Gal.putImageBytes(jpgBytes, name: fileName);

            if (mounted) {
              ScaffoldMessenger.of(context).clearSnackBars();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.white),
                      SizedBox(width: 12),
                      Text('Image saved to gallery!'),
                    ],
                  ),
                  backgroundColor: Colors.green,
                  duration: Duration(seconds: 2),
                ),
              );
            }
          }
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Storage permission required to save images'),
              backgroundColor: Colors.orange,
              action: SnackBarAction(
                label: 'Settings',
                onPressed: () => permissionManager.openAppSettings(),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving image: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
      if (kDebugMode) {
        debugPrint('Error saving image: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  void _toggleLike() {
    final postId = 'quote_${widget.category}_${widget.index}';
    final currentBg = widget.backgroundImages[widget.backgroundNotifier.value];

    widget.favoritesProvider.toggleFavorite(postId, widget.quote, currentBg);
    widget.likedNotifier.value = widget.favoritesProvider.isFavorite(postId);

    HapticFeedback.lightImpact();

    if (widget.favoritesProvider.tapSoundEnabled) {
      SystemSound.play(SystemSoundType.click);
    }
  }

  Future<void> _copyToClipboard() async {
    try {
      await Clipboard.setData(
        ClipboardData(text: _cleanQuoteText(widget.quote)),
      );

      if (widget.favoritesProvider.tapSoundEnabled) {
        SystemSound.play(SystemSoundType.click);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check, color: Colors.white),
                SizedBox(width: 12),
                Text('Quote copied to clipboard!'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to copy: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _shareQuote() async {
    try {
      await SharePlus.instance.share(
        ShareParams(
          text:
              '${_cleanQuoteText(widget.quote)}\n\n💕 Shared from Charm Shots - The ultimate pickup lines app!',
          subject:
              'Check out this ${widget.category.toLowerCase()} pickup line!',
        ),
      );

      if (widget.favoritesProvider.tapSoundEnabled) {
        SystemSound.play(SystemSoundType.click);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: widget.backgroundNotifier,
      builder: (context, currentBackgroundIndex, child) {
        return ValueListenableBuilder<bool>(
          valueListenable: widget.likedNotifier,
          builder: (context, isLiked, child) {
            return Container(
              height: widget.cardHeight,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.15),
                    spreadRadius: 0,
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Expanded(
                    flex: 82,
                    child: _buildBlackContentArea(currentBackgroundIndex),
                  ),
                  Expanded(flex: 18, child: _buildVisibleActionArea(isLiked)),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildBlackContentArea(int currentBackgroundIndex) {
    final cleanedText = _cleanQuoteText(widget.quote);

    return GestureDetector(
      onTap: widget.onBackgroundChange,
      child: Stack(
        children: [
          Screenshot(
            controller: _screenshotController,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                image: currentBackgroundIndex < widget.backgroundImages.length
                    ? DecorationImage(
                        image: AssetImage(
                          widget.backgroundImages[currentBackgroundIndex],
                        ),
                        fit: BoxFit.cover,
                        filterQuality:
                            FilterQuality.low, // Optimize for performance
                        colorFilter: ColorFilter.mode(
                          Colors.black.withValues(
                            alpha: 0.3,
                          ), // Lighter overlay for more visible background
                          BlendMode.darken,
                        ),
                      )
                    : null,
              ),
              child: Stack(
                children: [
                  // Removed decorative quote marks for cleaner look
                  Column(
                    children: [
                      Expanded(
                        child: Center(
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal:
                                  28, // Consistent padding without quotes
                              vertical: 20,
                            ),
                            child: Text(
                              cleanedText, // Using cleaned text (no quotes)
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 24, // Consistent larger font size
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                height: 1.4,
                                letterSpacing: 0.3,
                                shadows: [
                                  Shadow(
                                    offset: Offset(0, 2),
                                    blurRadius: 4,
                                    color: Colors.black54, // Simplified shadow
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16, right: 16),
                        child: Align(
                          alignment: Alignment.bottomRight,
                          child: Text(
                            '@Charm Shorts',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.white38, // Simplified color
                              fontWeight: FontWeight.w400,
                              letterSpacing: 0.3,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Positioned(top: 16, right: 16, child: _buildCompactEditButton()),
        ],
      ),
    );
  }

  Widget _buildCompactEditButton() {
    return GestureDetector(
      onTap: () {
        showEnhancedEditDialog(
          context: context,
          initialText: widget.quote,
          onSave: (newText, newStyle) {
            widget.onQuoteUpdate(newText, newStyle);
          },
        );
      },
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.95),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Icon(Icons.edit, color: Colors.black87, size: 20),
      ),
    );
  }

  Widget _buildVisibleActionArea(bool isLiked) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildVisibleActionButton(
            icon: isLiked ? Icons.favorite : Icons.favorite_border,
            label: 'Like',
            isActive: isLiked,
            onTap: _toggleLike,
          ),
          _buildVisibleActionButton(
            icon: _isSaving ? Icons.hourglass_empty : Icons.download,
            label: 'Save',
            onTap: _isSaving ? null : _saveAsJPG,
          ),
          _buildVisibleActionButton(
            icon: Icons.copy,
            label: 'Copy',
            onTap: _copyToClipboard,
          ),
          _buildVisibleActionButton(
            icon: Icons.share,
            label: 'Share',
            onTap: _shareQuote,
          ),
        ],
      ),
    );
  }

  Widget _buildVisibleActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
    bool isActive = false,
  }) {
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          splashColor: isActive && label == 'Like'
              ? Colors.red.withValues(alpha: 0.3)
              : Colors.grey.withValues(alpha: 0.1),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: isActive && label == 'Like'
                      ? Colors.red
                      : Colors.black87,
                  size: 22,
                ),
                const SizedBox(height: 2),
                Text(
                  label,
                  style: TextStyle(
                    color: isActive && label == 'Like'
                        ? Colors.red
                        : Colors.black87,
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
